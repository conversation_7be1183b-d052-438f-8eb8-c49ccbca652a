'use client';

import React, { Fragment, useState } from 'react';
import Wrapper from '@/components/wrapper/wrapper';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { Divider } from '@/components/divider/divider';
import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import { BasketSummaryItem } from '@/src/deliveroo/utils/helpers';
import { usePlanSelection } from '@/src/deliveroo/app/signup/context/SignupContext';
import { SignupProgressBar } from '@/src/deliveroo/app/signup/_components/signup-progress-bar/signup-progress-bar';
import { PlanCard } from '@/src/deliveroo/app/signup/_components/plan-card/planCard';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { ChevronDown, ChevronUp } from '@/icons/icons';
import { PlanCardSkeleton } from '@/src/deliveroo/app/signup/_components/plan-card-skeleton/plan-card-skeleton';
import { AuthRouteButton } from '@/components/go-to-billing-button/auth-route-button';
import { OrderSummary } from '@/src/deliveroo/app/signup/_components/order-summary/order-summary';
import { RequireDeliverooRiderAuth } from '@/components/require-auth/require-deliveroo-rider-auth';
import { CannotFetchAlert } from '@/components/cannot-fetch-alert/cannot-fetch-alert';
import { MAX_MAIN_PLAN_QUANTITY } from '@/src/deliveroo/app/signup/_reducer/reducer';

export default function PlanSelection() {
  const {
    planSelectionState,
    incrementMainPlan,
    decrementMainPlan,
    isPlanDataPending,
    planDataError,
    orderSummary,
    unlimitedPlanPrice,
    planName,
    quantityAvailableToPurchase
  } = usePlanSelection(MAX_MAIN_PLAN_QUANTITY);

  if (planDataError) {
    return (
      <CannotFetchAlert message="We could not fetch plans. Please try again" />
    );
  }

  return (
    <RequireDeliverooRiderAuth>
      <Wrapper>
        <div>
          <Divider className="m-0! mx-auto! w-[90%] lg:hidden" />
          <ConditionalWrapper className="left-column-inner rounded border-none shadow-none">
            <SignupProgressBar />
            <h1 className="mt-2 mb-4">Plan selection</h1>
            {isPlanDataPending ? (
              <PlanCardSkeleton />
            ) : (
              <>
                <PlanCard
                  planName={planName}
                  price={unlimitedPlanPrice}
                  quantity={planSelectionState.mainPlanQuantity}
                  incrementMainPlan={incrementMainPlan}
                  decrementMainPlan={decrementMainPlan}
                  quantityAvailableToPurchase={quantityAvailableToPurchase}
                />
                <p className="mt-6 text-center">
                  No other plans available
                </p>
                <Divider className="my-6 hidden lg:block" />
                <div className="bottom-summary-wrapper">
                  <TotalCostBottom
                    totalCost={orderSummary.totalCost}
                    basketSummary={orderSummary.basketSummary}
                    isPending={isPlanDataPending}
                  />
                  <div className="px-6 lg:px-0">
                    <AuthRouteButton
                      authPath={ROUTES_CONFIG['payment'].path}
                      fallbackPath={ROUTES_CONFIG['register'].path}
                      text="Continue"
                      className="border-none"
                    />
                  </div>
                </div>
              </>
            )}
          </ConditionalWrapper>
        </div>
        <div className="order-1 lg:order-2">
          <OrderSummary
            isPending={isPlanDataPending}
            basketSummary={orderSummary.basketSummary}
            totalCost={orderSummary.totalCost}
          />
        </div>
      </Wrapper>
    </RequireDeliverooRiderAuth>
  );
}

interface OrderSummaryMobileWrapperProps {
  isPending: boolean | undefined;
  basketSummary: BasketSummaryItem[];
  totalCost: string;
  toggleSummary: boolean;
  setToggleSummary: React.Dispatch<React.SetStateAction<boolean>>;
}

function OrderSummaryMobileWrapper({
  isPending,
  basketSummary,
  totalCost,
  setToggleSummary,
  toggleSummary
}: OrderSummaryMobileWrapperProps) {
  if (isPending) {
    return <div className="mx-auto h-9 w-full animate-pulse bg-gray-300"></div>;
  }

  return (
    <OrderSummaryMobile
      setToggleSummary={setToggleSummary}
      toggleSummary={toggleSummary}
      basketSummary={basketSummary}
      totalCost={totalCost}
    />
  );
}

interface OrderSummaryComponentProps {
  totalCost: string;
  basketSummary: BasketSummaryItem[];
}

interface OrderSummaryMobileProps extends OrderSummaryComponentProps {
  toggleSummary: boolean;
  setToggleSummary: React.Dispatch<React.SetStateAction<boolean>>;
}

function OrderSummaryMobile({
  toggleSummary,
  basketSummary
}: OrderSummaryMobileProps) {
  return (
    <section
      className="bg-teal-light mt-6 w-full p-4 px-6"
      aria-label="Basket summary"
    >
      <div
        id="order-summary-panel"
        aria-hidden={!toggleSummary}
        hidden={!toggleSummary}
      >
        <ol className="space-y-4">
          {basketSummary.map((item) => {
            return (
              <Fragment key={item.planId}>
                <OrderSummaryRowMobile
                  planName={item.planName}
                  planPrice={item.planPrice}
                  quantity={item.quantity}
                />
              </Fragment>
            );
          })}
        </ol>
      </div>
    </section>
  );
}

interface OrderSummaryRowProps {
  planName: string;
  planPrice: number;
  quantity: number;
}

function OrderSummaryRowMobile({
  planName,
  planPrice,
  quantity
}: OrderSummaryRowProps) {
  const totalPrice = planPrice * quantity;

  return (
    <li className="flex items-start justify-between">
      <div className="flex items-start gap-2">
        <span className="bg-teal-darker text-xxxs rounded-xs px-[5px] py-[2px]">
          {quantity}x
        </span>
        <div className="flex flex-col">
          <p className="text-xxxs font-medium">{planName} SIM plan</p>
        </div>
      </div>
      <strong className="text-default">£{totalPrice}</strong>
    </li>
  );
}

interface TotalCostBottomProps {
  totalCost: string;
  basketSummary: BasketSummaryItem[];
  isPending?: boolean;
}

function TotalCostBottom({
  totalCost,
  basketSummary,
  isPending
}: TotalCostBottomProps) {
  const isMobileDevice = useMediaQuery(1024);
  const [toggleSummary, setToggleSummary] = useState(false);
  const [pounds, decimals] = Number(totalCost).toFixed(2).split('.');

  return (
    <footer
      role="contentinfo"
      aria-label={`Total cost £${pounds}.${decimals} a month, including VAT`}
      className="flex flex-col items-end justify-between"
    >
      <dl
        className="font-heading flex w-full flex-col items-end px-6 lg:px-0"
        aria-hidden="true"
      >
        <div className="flex w-full items-end justify-between">
          <dt className="flex items-center gap-4 text-xs font-bold lg:px-0 lg:text-base">
            {isMobileDevice ? (
              <button
                className="flex items-center gap-4"
                onClick={() => setToggleSummary(!toggleSummary)}
              >
                <span>Total cost</span>
                {toggleSummary ? <ChevronUp /> : <ChevronDown />}
              </button>
            ) : (
              <span>Total cost</span>
            )}
          </dt>
          <dd className="text-xs font-bold lg:text-base">
            <strong data-testid="total-cost-bottom">
              £{pounds}.
              <sup className="align-baseline text-[18px] lg:text-xs" style={{ verticalAlign: 'super', fontSize: '0.75em' }}>
                {decimals}{' '}
              </sup>
            </strong>
            <span className="text-default font-primary text-xs font-normal lg:text-[18px]">
              a month
            </span>
          </dd>
        </div>
        <span className="text-xxxs font-primary text-right">
          (including VAT)
        </span>
      </dl>
      {toggleSummary && (
        <OrderSummaryMobileWrapper
          isPending={isPending}
          basketSummary={basketSummary}
          totalCost={totalCost}
          setToggleSummary={setToggleSummary}
          toggleSummary={toggleSummary}
        />
      )}
    </footer>
  );
}
